from re import <PERSON><PERSON> as re_Pattern
from typing import Tuple, Type, Union, List, Dict, Any, Optional

from .utils import (
    normalize_date,
    normalize_dns_question_name,
    normalize_time,
)

from .utils_classes import (
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
)

from .utils_patterns import (
    DAEMON_PATTERN,
    FILTERLOG_PATTERN,
    SNORT_PATTERN,
    SQUID_PATTERN,
    USERAUDIT_PATTERN,
    USERNOTICE_PATTERN,
    USERWARNING_PATTERN,
    ROUTER_PATTERN,
    ROUTERBOARD_PATTERN,
    SWITCH_PATTERN,
    VMWARE_PATTERN,
    WINDOW<PERSON>ERVER_PATTERN,
    WS_AN_AD_PATTERN,
    WS_SW_PATTERN,
    DHCP_PATTERN,
    DNS_PATTERN,
    DNS_REST_PATTERN,
    VPNSERVER_PATTERN,
)


def _is_invalid_ln(ln: str) -> bool:
    '''
    Checks if ln does not have a valid structure
    and therefore is not a proper candidate to be parsed.

    Args:
        ln (str): a log line

    Returns:
        True: for lines containing the given strings
              or does not start with a digit
        False: otherwise
    '''
    ## __HAS_TEST__

    # if has_non_printable_bytes(ln):
    #     return True

    if not ln or \
       'ERROR name exceeds safe print buffer length' in ln or \
       'ERROR length byte' in ln or \
       'leads outside message' in ln or \
       'Exiting on signal' in ln or \
       'Now monitoring attacks' in ln or \
       'spp_arpspoof' in ln or \
       'because it is a directory, not a file' in ln or \
       not ln[0].isdigit():
        return True
    return False

def _invalid_line_sections(
    object_list_of_names_and_addresses: List[str],

    event_types: List[str],
    filterby_is_in_alert_type: bool,
    filterby: str,

    line_object: str,
    line_event_type: str,
    line_alert_type: str,
):
    ## early return if none of them exists
    if not (event_types or filterby or line_object):
        return False

    ## added 'if line_object' because
    ## DHCP, DNS and VPN Server have no line_object
    if line_object and \
       line_object not in object_list_of_names_and_addresses:
        return True

    if event_types and \
       line_event_type not in event_types:
        return True

    if filterby_is_in_alert_type and \
       filterby not in line_alert_type:
        return True

    return False

def _extract_matches_from_pattern(
    string: str,
    pattern: re_Pattern,
) -> Optional[Tuple[str, ...]]:
    '''
    Applies a regex pattern to a string
    which is often a log line or a part of it
    and returns the matched groups if any.

    Args:
        string (str): The input string to match against.
        pattern (re_Pattern): A compiled regular expression pattern.

    Returns:
        Optional[Tuple[str, ...]]: Tuple of matched groups if pattern matches; otherwise None.
    '''
    matches = pattern.match(string)
    ## possible values:
    ##   - <re.Match object; span=(0, 163), match='2020-01-30 00:01:02 Sensor-1 (auth/alert) [snort]>
    ##   - None

    return matches.groups() if matches else None
    ## possible values of matches.groups():
    ##   - ('2020-01-30', '00:01:02', 'Sensor-1', '(auth/alert)', '[snort]', '1:1325:14', ...)
    ##   - ('SOMEADDS',)
    ##   - None

def _parse_snort(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, SNORT_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (daemon/err), ...
    line_alert_type = splited[4]  ## [snort]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    ## NOTE: as we're doing this quite frequently,
    ##       and keys may be mostly missing or hits are rare,
    ##       we check `if line_sensor in dict:` first
    ##       to avoid allocation of sensor in fallback return
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],   ## 2023-05-12
            splited[1],   ## 23:36:10
            splited[5],   ## gidsid
            splited[6],   ## description
            splited[7],   ## classification
            splited[8],   ## priority
            splited[9],   ## protocol
            splited[10],  ## source ip
            splited[11],  ## source port
            splited[12],  ## destination ip
            splited[13],  ## destination port
        ),
    )

def _parse_daemon(
    ln,
    object_list_of_names_and_addresses,
    object_dict_of_addresses_and_names,
    event_types,
    filterby_is_in_alert_type,
    filterby,
):
    if not (splited := _extract_matches_from_pattern(ln, DAEMON_PATTERN)):
        return (None, None)

    line_sensor     = splited[2]  ## Sensor-1 OR ***********
    line_event_type = splited[3]  ## (daemon/err), ...
    line_alert_type = splited[4]  ## [radiusd]

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_sensor,
        line_event_type,
        line_alert_type,
    ):
        return (None, None)

    ## *********** -> Sensor-1 (JUMP_1)
    if line_sensor in object_dict_of_addresses_and_names:
        line_sensor = object_dict_of_addresses_and_names[line_sensor]

    return (
        line_sensor,

        ## row
        (
            splited[0],  ## 2023-05-12,
            splited[1],  ## 23:36:10
            line_event_type,
            line_alert_type,
            splited[5],  ## message
        ),
    )

_PRECOMPUTED_VALUES = {
    cls: (
        cls.SLUG.value,
        cls.FILTERBY.value,
        cls.FILTERBY_IS_IN_ALERT_TYPE.value,
        cls.EVENT_TYPES.value,
    )
    for cls in (
        FilterLogConfig,
        SnortConfig,
        DaemonConfig,
        VPNServerConfig,
        WindowsServerConfig,
        DNSConfig,
        DHCPConfig,
        UserWarningConfig,
        SwitchConfig,
        UserNoticeConfig,
        UserAuditConfig,
        SquidConfig,
        RouterConfig,
        RouterBoardConfig,
        VMwareConfig,
    )
}

_PARSERS = {
    ## __ORDER_OF_CLASSES__
    SnortConfig.SLUG.value: _parse_snort,
    DaemonConfig.SLUG.value: _parse_daemon,
}

def parse_ln(
    ln: str,
    cls: Type,

    ## removed `None` defaults
    ## in order to optimize function signature
    object_list_of_names_and_addresses: List[str],
    object_dict_of_addresses_and_names: Dict[str, str],
) -> Tuple[Union[str, None], Union[Tuple[Any, ...], None]]:
    ## __HAS_TEST__

    if _is_invalid_ln(ln):
        return (None, None)

    slug, \
    filterby, \
    filterby_is_in_alert_type, \
    event_types = _PRECOMPUTED_VALUES[cls]

    if filterby and \
       not filterby_is_in_alert_type and \
       filterby not in ln:
        return (None, None)

    ## get which function to run
    func = _PARSERS.get(slug)

    if func is None:
        return (None, None)

    return func(
        ln,
        object_list_of_names_and_addresses,
        object_dict_of_addresses_and_names,
        event_types,
        filterby_is_in_alert_type,
        filterby,
    )
